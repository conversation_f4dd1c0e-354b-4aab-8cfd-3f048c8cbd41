import GeneralNodes from "@/apps/Common/GeneralNotdes";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Modal, Row, Typography } from "antd";
import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams, useSearchParams } from "react-router-dom";
import endPoints from "../../../EndPoints";
import { useQueryClient } from "react-query";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { connectEdge, deleteEdge, updateEdgeWithPut } from "../../../Services";
import TransationIndex from "../TransationIndex";
import ListItems from "../Rules/ListItems";

const AddOrUpdateEdge: FC<{ selectedRecord?: any; onFinish: any }> = ({
  selectedRecord,
  onFinish,
}) => {
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const [selectedFromNodeId, setSelectedFromNodeId] = useState<null | string>(
    null
  );
  const {Text} = Typography
  const [selectedToNodeId, setSelectedToNodeId] = useState<null | string>(null);
  const { t } = useTranslation();
  const { workFlowId } = useParams();
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    try {
      formValues["FlowId"] = workFlowId;
      if (selectedRecord && selectedRecord?.type !== "connect") {
        formValues["Id"] = selectedRecord?.id;
        await updateEdgeWithPut(formValues);
      } else {
        await connectEdge(formValues);
      }
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

      queryClient.resetQueries({
        queryKey: endPoints.getNodeEdgeList,
        exact: false,
      });
      onFinish();
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({
        FromNodeId: selectedRecord?.source,
        ToNodeId: selectedRecord?.target,
        Name: selectedRecord?.label,
      });
    }
  }, [selectedRecord]);

  const deleteEdgeConfirm = () => {
    Modal.confirm({
      title: t("profession.warning"),
      icon: null,
      content: t("profession.deleteModalDesc"),
      okText: t("profession.delete"),
      cancelText: t("profession.cancel"),
      onOk: async () => {
        try {
          await deleteEdge({ ...selectedRecord, Id: selectedRecord?.id });
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getNodeEdgeList,
            exact: false,
          });
          form.resetFields();
          onFinish();
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  useEffect(() => {
    if (selectedRecord?.id) {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set("edgeId", selectedRecord.id);
      setSearchParams(newParams);
    }
  }, [selectedRecord]);

  return (
    <>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            <MazakaInput
              xs={24}
              label={t("workFlow.name")}
              placeholder={t("workFlow.name")}
              name={"Name"}
              rules={[{ required: true, message: "" }]}
            />
            <GeneralNodes
              xs={24}
              label={t("workFlow.fromNode")}
              placeholder={t("workFlow.fromNode")}
              name={"FromNodeId"}
              rules={[{ required: true, message: "" }]}
              externalValueId={workFlowId}
              onChange={(value: string) => {
                setSelectedFromNodeId(value);
              }}
              excludeIds={[selectedToNodeId]}
              allowClear
            />
            <GeneralNodes
              allowClear
              label={t("workFlow.toNode")}
              placeholder={t("workFlow.toNode")}
              xs={24}
              name={"ToNodeId"}
              rules={[{ required: true, message: "" }]}
              externalValueId={workFlowId}
              excludeIds={[selectedFromNodeId]}
              onChange={(value: string) => {
                setSelectedToNodeId(value);
              }}
            />

            <Col xs={24} className="!flex gap-1">
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
                status="save"
              >
                {t("fileManager.save")}
              </MazakaButton>
              {selectedRecord && selectedRecord?.type !== "connect" && (
                <MazakaButton
                  htmlType="button"
                  status="error"
                  onClick={deleteEdgeConfirm}
                >
                  {t("workFlow.deleteTransition")}
                </MazakaButton>
              )}
            </Col>
            {selectedRecord && selectedRecord?.type !== "connect" && (
              <Col xs={24} className="!mt-4">
                <Row gutter={[0, 15]}>
                  
                  <Col xs={24}>
                    <TransationIndex />
                  </Col>
                  <Col xs={24}>
                    <ListItems />
                  </Col>
                </Row>
              </Col>
            )}
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateEdge;
