import {  useEffect, useState } from "react";
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Node,
  MiniMap,
  Edge,
} from "@xyflow/react";

import "@xyflow/react/dist/style.css";
import { Drawer } from "antd";
import ResizeableNodeSelected from "./Nodes/ResizeableNodeSelected";
import { useGetNodeEdges, useGetWorkFlowNodes } from "../../ServerSideStates";
import { useParams, useSearchParams } from "react-router-dom";
import AddOrUpdateNodeButton from "./Nodes/AddOrUpdateNodeButton";
import AddOrUpdateNode from "./Nodes/AddOrUpdateNode";
import AddOrUpdateEdgeButton from "./Edges/AddOrUpdateEdgeButton";
import AddOrUpdateEdge from "./Edges/AddOrUpdateEdge";
import { useTranslation } from "react-i18next";

import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { updateNodeOnWorkFlow } from "../../Services";



const EdgeTypesFlow = () => {
  const [searchParams,setSearchParams] = useSearchParams()
  const {t} = useTranslation()
  const [isShowEditNodeDrawer, setIsShowEditNodeDrawer] = useState(false);
  const [isShowEditEdgeDrawer, setIsShowEditEdgeDrawer] = useState(false);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<any>(null);
  const { workFlowId } = useParams();

  const workFlowNodes = useGetWorkFlowNodes({
    PageNumber: 1,
    PageSize: 100,
    FlowId: workFlowId,
  });
  const nodeEdges = useGetNodeEdges({
    PageNumber: 1,
    PageSize: 100,
  });

  useEffect(() => {
    if (workFlowNodes.data) {
      const allNodes = workFlowNodes.data?.Value;
      const formatedNodeData = allNodes?.map((item: any) => {
        let backgroundColor = "";
        let color = "";
        if (item.Type === "Start") {
          backgroundColor = "#d1d5db";
          color = "black";
        } else if (item.Type === "Process") {
          backgroundColor = "white";
          color = "black";
        } else if (item.Type === "End") {
          backgroundColor = "#0096d1";
          color = "white";
        }

        return {
          id: item?.Id,
          data: { label: item?.Name || "", type: item.Type,x: item?.PositionX,y: item?.PositionY,flowId:item?.FlowId },
          position: { x: item?.PositionX||0, y: item?.PositionY||0 },
          type: "ResizeableNodeSelected",
          style: { backgroundColor, color },
          // draggable:item.Type === "Start"?false:true
        };
      });
      setNodes(formatedNodeData);
    }
  }, [workFlowNodes.data]);

  useEffect(() => {
    if (nodeEdges?.data) {
      let allNodeEdges = nodeEdges.data?.Value || [];
      let formattedNodeEdges = allNodeEdges?.map((item: any) => {
        return {
          type: "smoothstep",
          markerEnd: {
            type: "arrowclosed", 
          },
          source: item.FromNodeId,
          target: item.ToNodeId,
          id: item.Id,
          label: item.Name,
          animated: true,
        };
      });
      setEdges(formattedNodeEdges);
    }
  }, [nodeEdges.data]);

  const onConnect = async(edge:any)=>{
  await setSelectedEdge({
   ...edge,type:"connect"
  })
  setIsShowEditEdgeDrawer(true)

  }

  const handleNodeClick = async (_: any, node: Node) => {
    if (node?.data?.type !== "Start") {
      await setSelectedNode(node);
      setIsShowEditNodeDrawer(true);
      setToStatus("");
    }
  };

  const onEdgeClick = async (_: any, edge: Edge) => {
    await setSelectedEdge(edge);
    setIsShowEditEdgeDrawer(true);
  };
  const nodeTypes = {
    ResizeableNodeSelected,
  };

  const nodeColor = (node: any) => {
    if (node?.data?.type === "Start") {
      return "#d1d5db";
    }
    if (node?.data?.type === "End") {
      return "#0096d1";
    }
    return "black";
  };

  const handleDropNode = async(event:any,node:any)=>{
    const currentData = {...node?.data}
    const currentPosition = node.position
   
            console.log("Yeni konum:", node.position); // { x: ..., y: ... }
    try {
      const data = {
        "FlowId":currentData.FlowId,
        "Name": currentData?.label,
        "PositionX": currentPosition?.x,
        "PositionY": currentPosition?.y,
        "NodeType": currentData?.type==="Start"?1:currentData?.type==="End"?3:2,
        Id:node?.id,
      }
      await updateNodeOnWorkFlow(data)
    } catch (error) {
      showErrorCatching(error,null,false,t)
    }
  }

  

  return (
    <div className="!h-screen !flex">
      <div style={{ flexGrow: 1 }}>
        <ReactFlow
          nodes={nodes}
          
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onEdgeClick={onEdgeClick}
          onConnect={onConnect}
          onNodeClick={handleNodeClick}
          nodeTypes={nodeTypes}
          onNodeDragStop={handleDropNode}
          fitView
          defaultEdgeOptions={{ type: 'smoothstep' }}
          minZoom={0.9}
          style={{ backgroundColor: "#F7F9FB" }}
          snapToGrid={true}
        >
          <MiniMap
            nodeColor={nodeColor}
            nodeStrokeWidth={3}
            zoomable
            pannable
            className="!mb-24"
          />
          <Controls className="!mb-24" />
          <Background />
        </ReactFlow>
        <div>
          <AddOrUpdateEdgeButton />
          <AddOrUpdateNodeButton />
        </div>
      </div>

      <Drawer
       title={t("workFlow.editNode")}
        open={isShowEditNodeDrawer}
        onClose={() => {
          setIsShowEditNodeDrawer(false);
        }}
      >
        <AddOrUpdateNode
          selectedNode={selectedNode}
          onFinish={() => {
            setIsShowEditNodeDrawer(false);
          }}
        />
        
      </Drawer>

      <Drawer
       title={t("workFlow.editTransition")}
        open={isShowEditEdgeDrawer}
        onClose={() => {
          const newParams = new URLSearchParams(searchParams.toString());
          newParams.delete("edgeId");
          setSearchParams(newParams);
          setIsShowEditEdgeDrawer(false);
        }}
        width={"50%"}
      >
        <AddOrUpdateEdge
          selectedRecord={selectedEdge}
          onFinish={() => {
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.delete("edgeId");
            setSearchParams(newParams);
            setIsShowEditEdgeDrawer(false);
          }}
        />
      </Drawer>

     
    </div>
  );
};

export default EdgeTypesFlow;
