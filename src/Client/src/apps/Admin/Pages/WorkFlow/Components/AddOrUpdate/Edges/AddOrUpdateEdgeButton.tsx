import { BranchesOutlined } from "@ant-design/icons";
import { Drawer, FloatButton, Tooltip } from "antd";
import { FC, useState } from "react";
import { useTranslation } from "react-i18next";
import AddOrUpdateEdge from "./AddOrUpdateEdge";
import { useSearchParams } from "react-router-dom";


const AddOrUpdateEdgeButton: FC<{
  selectedRecord?: any;
}> = ({ selectedRecord }) => {
  const { t } = useTranslation();
  const [isShowDrawer, setIsShowDrawer] = useState(false);
  const[searchParams,setSearchParams] = useSearchParams()

  return (
    <>
    <Tooltip
    title={t("workFlow.addTransition")}
    >

      <FloatButton
        icon={<BranchesOutlined />}
       type="primary"
        className="!bg-[#0096d1]"
        onClick={() => {
          setIsShowDrawer(true);
        }}
        style={{
          top: "7%", // üst kenara hizala
          position: 'fixed', // sabitle
          left: '53.4%', // yatayda ortala
          transform: 'translateX(-50%)', // tam ortalamak için
          zIndex: 1000, // üstte kalması için isteğe bağlı
          
        }}
      />
    </Tooltip>

      <Drawer
        title={t("workFlow.addTransition")}
        open={isShowDrawer}
        onClose={() => {
         
          setIsShowDrawer(false);
        }}
      >
        <AddOrUpdateEdge
          selectedRecord={selectedRecord}
          onFinish={() => {
         
            setIsShowDrawer(false);
            
          }}
        />
      </Drawer>
    </>
  );
};

export default AddOrUpdateEdgeButton;
